<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换码 - Miao<PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 2rem 0;
            min-height: 100vh;
        }

        .redeem-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 2rem;
            text-align: center;
        }

        .card-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }

        .card-header .subtitle {
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        .card-body {
            padding: 3rem;
        }

        .redeem-input-group {
            position: relative;
            margin-bottom: 2rem;
        }

        .redeem-input {
            width: 100%;
            padding: 1.5rem;
            font-size: 1.2rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            text-align: center;
            letter-spacing: 3px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }

        .redeem-input:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
            transform: translateY(-2px);
        }

        .redeem-btn {
            width: 100%;
            padding: 1.2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .redeem-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            background: linear-gradient(135deg, #218838, #1ea085);
        }

        .redeem-btn:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .user-info {
            background: rgba(40, 167, 69, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .points-display {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 0.8rem 1.5rem;
            color: #6c757d;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            color: #495057;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .history-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .history-item {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .success-animation {
            animation: successPulse 0.6s ease-in-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .floating-icon {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ffc107;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .tips-section {
            background: rgba(13, 202, 240, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .tips-section h5 {
            color: #0dcaf0;
            margin-bottom: 1rem;
        }

        .tips-section ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .tips-section li {
            margin-bottom: 0.5rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">
        <i class="fas fa-arrow-left me-2"></i>返回首页
    </a>

    <div class="container main-container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="redeem-card">
                    <div class="card-header">
                        <div class="floating-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h1><i class="fas fa-ticket-alt me-3"></i>兑换码</h1>
                        <p class="subtitle mb-0">输入兑换码获取积分奖励</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- 用户信息 -->
                        <div class="user-info">
                            <h5 class="mb-2">
                                <i class="fas fa-user me-2"></i>{{ current_user.username }}
                            </h5>
                            <div class="points-display">
                                <i class="fas fa-coins me-2"></i>
                                <span id="userPoints">{{ current_user.points }}</span> 积分
                            </div>
                        </div>

                        <!-- 兑换码输入 -->
                        <div class="redeem-input-group">
                            <input type="text" 
                                   id="redemptionCodeInput" 
                                   class="redeem-input" 
                                   placeholder="请输入兑换码"
                                   maxlength="20"
                                   autocomplete="off">
                        </div>

                        <button type="button" id="redeemBtn" class="redeem-btn">
                            <i class="fas fa-magic me-2"></i>立即兑换
                        </button>

                        <!-- 使用提示 -->
                        <div class="tips-section">
                            <h5><i class="fas fa-lightbulb me-2"></i>使用提示</h5>
                            <ul>
                                <li>兑换码不区分大小写</li>
                                <li>每个兑换码只能使用一次</li>
                                <li>过期的兑换码无法使用</li>
                                <li>兑换成功后积分会立即到账</li>
                            </ul>
                        </div>

                        <!-- 兑换历史 -->
                        <div class="history-section">
                            <h5><i class="fas fa-history me-2"></i>兑换历史</h5>
                            <div id="redemptionHistory">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const redemptionCodeInput = document.getElementById('redemptionCodeInput');
            const redeemBtn = document.getElementById('redeemBtn');
            const userPointsSpan = document.getElementById('userPoints');

            // 自动聚焦到输入框
            redemptionCodeInput.focus();

            // 输入框自动转换为大写
            redemptionCodeInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });

            // 回车键兑换
            redemptionCodeInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    redeemBtn.click();
                }
            });

            // 兑换按钮点击事件
            redeemBtn.addEventListener('click', function() {
                const code = redemptionCodeInput.value.trim();

                if (!code) {
                    showAlert('请输入兑换码', 'warning');
                    redemptionCodeInput.focus();
                    return;
                }

                // 禁用按钮并显示加载状态
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>兑换中...';

                // 发送兑换请求
                fetch('/redeem_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // 恢复按钮状态
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-magic me-2"></i>立即兑换';

                    if (data.success) {
                        // 兑换成功
                        showAlert(`兑换成功！获得 ${data.points_gained} 积分`, 'success');
                        
                        // 更新积分显示
                        userPointsSpan.textContent = data.new_points;
                        
                        // 添加成功动画
                        document.querySelector('.user-info').classList.add('success-animation');
                        setTimeout(() => {
                            document.querySelector('.user-info').classList.remove('success-animation');
                        }, 600);

                        // 清空输入框
                        redemptionCodeInput.value = '';
                        
                        // 刷新兑换历史
                        loadRedemptionHistory();
                    } else {
                        // 兑换失败
                        showAlert(data.message, 'danger');
                        redemptionCodeInput.focus();
                    }
                })
                .catch(error => {
                    // 恢复按钮状态
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-magic me-2"></i>立即兑换';
                    
                    showAlert('兑换失败，请稍后重试', 'danger');
                    redemptionCodeInput.focus();
                });
            });

            // 加载兑换历史
            function loadRedemptionHistory() {
                fetch('/user/redemption_history')
                    .then(response => response.json())
                    .then(data => {
                        const historyContainer = document.getElementById('redemptionHistory');
                        
                        if (data.success && data.records && data.records.length > 0) {
                            let html = '';
                            data.records.slice(0, 5).forEach(record => {
                                const date = new Date(record.used_at).toLocaleString('zh-CN');
                                html += `
                                    <div class="history-item">
                                        <div>
                                            <strong>${record.code}</strong>
                                            <small class="text-muted d-block">${date}</small>
                                        </div>
                                        <div class="text-success">
                                            <i class="fas fa-plus me-1"></i>${record.points} 积分
                                        </div>
                                    </div>
                                `;
                            });
                            historyContainer.innerHTML = html;
                        } else {
                            historyContainer.innerHTML = `
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-inbox me-2"></i>暂无兑换记录
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        document.getElementById('redemptionHistory').innerHTML = `
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>加载失败
                            </div>
                        `;
                    });
            }

            // 显示提示信息
            function showAlert(message, type) {
                // 移除现有的提示
                const existingAlert = document.querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                // 创建新的提示
                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // 插入到卡片顶部
                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(alert, cardBody.firstChild);

                // 自动消失
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            }

            // 页面加载时获取兑换历史
            loadRedemptionHistory();
        });
    </script>
</body>
</html>
